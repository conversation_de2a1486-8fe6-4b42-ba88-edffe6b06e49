import { IComponentDescription } from '../types/index';
import { props } from './props';

export const JNTableOfAutoMeta: IComponentDescription = {
  componentName: 'ComparedTableTranspositionV2',
  title: '表格(接口转置)',
  docUrl: '',
  icon: 'https://cdn-h5.bananain.cn/icons/b013.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'ComparedTableTranspositionV2',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '表格(接口转置)',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b013.png',
      schema: {
        componentName: 'ComparedTableTranspositionV2',
        props: {
          dataSource: [],
          border: 1,
          id: null,
          settingButtons: true,
          columns: [],
        },
      },
    },
  ],
};

export default JNTableOfAutoMeta;
