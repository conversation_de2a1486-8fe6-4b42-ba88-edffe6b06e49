import { leftCardProps } from './defaultValue'; // 默认数据

export const JnIndicatorGroupBoxMeta = {
  componentName: 'JnIndicatorGroupBox',
  title: '卡片容器',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b027.png',
  devMode: 'proCode',
  category: '可分组指标容器',
  group: 'MelGeek组件',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JnIndicatorGroupBox',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: [
      {
        name: 'title',
        title: '容器标题',
        defaultValue: '容器标题',
        setter: 'StringSetter',
      },
      {
        name: 'isShowHeader',
        title: '显示标题',
        defaultValue: true,
        setter: 'BoolSetter',
      },
      {
        name: 'icon',
        title: '标题图标',
        defaultValue: '',
        setter: 'IconFontSetter',
      },

      {
        name: 'headerSize',
        title: '标题大小',
        defaultValue: 'big',
        setter: {
          componentName: 'RadioGroupSetter',
          props: {
            options: [
              {
                title: '大',
                value: 'big',
              },
              {
                title: '小',
                value: 'small',
              },
            ],
          },
        },
      },
      {
        name: 'tooltipControl',
        title: '标题备注',
        setter: 'BoolSetter',
      },
      {
        name: 'tooltipType',
        title: '备注类型',
        defaultValue: 'simpleText',
        condition: (target) => {
          return !!target.getProps().getPropValue('tooltipControl');
        },
        setter: {
          componentName: 'SelectSetter',
          props: {
            options: [
              {
                title: '短文本',
                value: 'simpleText',
              },
              {
                title: '长文本',
                value: 'longText',
              },
              {
                title: '文档',
                value: 'doc',
              },
            ],
          },
        },
      },
      {
        name: 'tooltipTitle',
        title: '备注标题',
        condition: (target) => {
          const isShow = !!target.getProps().getPropValue('tooltipControl');
          const tooltipType = target.getProps().getPropValue('tooltipType');
          const typePermit = tooltipType === 'longText' || tooltipType === 'doc';

          return isShow && typePermit;
        },
        setter: 'StringSetter',
      },
      {
        name: 'tooltip',
        title: '备注内容',
        condition: (target) => {
          const isShow = !!target.getProps().getPropValue('tooltipControl');
          const tooltipType = target.getProps().getPropValue('tooltipType');
          const typePermit = tooltipType === 'simpleText' || tooltipType === 'longText';

          return isShow && typePermit;
        },
        setter: 'TextAreaSetter',
      },
      {
        name: 'tooltipDocIcon',
        title: '标题图标',
        condition: (target) => {
          const isShow = !!target.getProps().getPropValue('tooltipControl');
          const tooltipType = target.getProps().getPropValue('tooltipType');
          const typePermit = tooltipType === 'doc';

          return isShow && typePermit;
        },
        setter: 'IconFontSetter',
      },
      {
        name: 'tooltipDocUrl',
        title: '文档链接',
        condition: (target) => {
          const isShow = !!target.getProps().getPropValue('tooltipControl');
          const tooltipType = target.getProps().getPropValue('tooltipType');
          const typePermit = tooltipType === 'doc';

          return isShow && typePermit;
        },
        setter: 'TextAreaSetter',
      },
      {
        name: 'moreOpen',
        title: '跳转',
        initialValue: false,
        setter: 'BoolSetter',
      },
      {
        name: 'linkShowText',
        title: '跳转文本',
        setter: 'StringSetter',
        condition: (target) => {
          return target.getProps().getPropValue('moreOpen') || false;
        },
      },
      {
        name: 'linkConfig',
        title: '跳转配置',
        setter: 'LinkBoxSetter',
        condition: (target) => {
          return target.getProps().getPropValue('moreOpen') || false;
        },
      },

      {
        name: 'showSlot',
        title: '插槽',
        initialValue: true,
        defaultValue: false,
        setter: 'BoolSetter',
      },
      {
        name: 'defaultShowChildren',
        title: '默认显示插槽',
        initialValue: true,
        defaultValue: false,
        setter: 'BoolSetter',
      },
      {
        name: 'cardHover',
        title: '卡片高亮',
        defaultValue: false,
        initialValue: true,
        setter: 'BoolSetter',
      },
      {
        name: 'cardBorder',
        title: '卡片边框',
        defaultValue: false,
        initialValue: true,
        setter: 'BoolSetter',
      },
      { ...leftCardProps },
      {
        name: 'style',
        title: '样式',
        display: 'accordion',
        setter: {
          componentName: 'StyleSetter',
        },
      },
    ],

    component: {
      isContainer: false,
      // disableBehaviors: '*',
      nestingRule: {},
    },
  },
  snippets: [
    {
      title: '卡片容器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b027.png',
      schema: {
        componentName: 'JnIndicatorGroupBox',
        props: {
          isShowHeader: true,
          showSlot: true,
          defaultShowChildren: true,
          cardHover: false,
          slotList: [
            {
              content: {
                type: 'JSSlot',
                id: 'jn_indicator_001' + Date.now(),
              },
              childrenContent: {
                type: 'JSSlot',
                id: 'jn_indicator_sub_001' + Date.now(),
              },
            },
            {
              content: {
                type: 'JSSlot',
                id: 'jn_indicator_002' + Date.now(),
              },
              childrenContent: {
                type: 'JSSlot',
                id: 'jn_indicator_sub_002' + Date.now(),
              },
            },
            {
              content: {
                type: 'JSSlot',
                id: 'jn_indicator_003' + Date.now(),
              },
              childrenContent: {
                type: 'JSSlot',
                id: 'jn_indicator_sub_003' + Date.now(),
              },
            },
            {
              content: {
                type: 'JSSlot',
                id: 'jn_indicator_004' + Date.now(),
              },
              childrenContent: {
                type: 'JSSlot',
                id: 'jn_indicator_sub_004' + Date.now(),
              },
            },
            {
              content: {
                type: 'JSSlot',
                id: 'jn_indicator_005' + Date.now(),
              },
              childrenContent: {
                type: 'JSSlot',
                id: 'jn_indicator_sub_005' + Date.now(),
              },
            },
            {
              content: {
                type: 'JSSlot',
                id: 'jn_indicator_006' + Date.now(),
              },
              childrenContent: {
                type: 'JSSlot',
                id: 'jn_indicator_sub_006' + Date.now(),
              },
            },
          ],
        },
      },
    },
  ],
};

export default JnIndicatorGroupBoxMeta;
