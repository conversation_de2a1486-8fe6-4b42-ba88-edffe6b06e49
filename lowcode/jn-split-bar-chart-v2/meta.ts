import props from './props';
import { JNSplitBarChartV2Snippets } from './snippets';

const JNSplitBarChartV2Meta = {
  componentName: 'JNSplitBarChart_V2',
  title: '蝴蝶图',
  category: '图表类',
  group: 'MelGeek组件',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b019.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNSplitBarChart_V2',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNSplitBarChartV2Snippets,
};

export default { ...JNSplitBarChartV2Meta, JNSplitBarChartV2Snippets };
