export const DEFAULT_SOURCE = {
  children: {
    type: 'JSSlot',
    value: [
      {
        componentName: 'JNCascadeLineChart',
        id: 'node_oclj5g2g3w4',
        props: {
          series: [
            {
              name: '销售金额',
              key: 'IdrrViORud',
              data: [
                {
                  y: 8722239.4139,
                  colorBy: '',
                },
                {
                  y: 9234485.7297,
                  colorBy: '',
                },
                {
                  y: 106488422.2828,
                  colorBy: '',
                },
                {
                  y: 56332447.94,
                  colorBy: '',
                },
                {
                  y: 32986716.1043,
                  colorBy: '',
                },
                {
                  y: 35797390.0937,
                  colorBy: '',
                },
                {
                  y: 16721384.8775,
                  colorBy: '',
                },
              ],
              format: {
                specifier: ',.0f',
                suffix: '',
                isAuto: false,
                excelFormat: '#,###0""',
              },
              yAxis: 0,
              metric_additional: false,
              value: '0',
              numberValue: 0,
            },
            {
              name: '同比',
              key: 'EZgzfGJqdx',
              data: [
                {
                  y: 0.637939,
                  colorBy: '',
                },
                {
                  y: 0.606887,
                  colorBy: '',
                },
                {
                  y: 0.356509,
                  colorBy: '',
                },
                {
                  y: 0.454604,
                  colorBy: '',
                },
                {
                  y: 0.801676,
                  colorBy: '',
                },
                {
                  y: 0.556481,
                  colorBy: '',
                },
                {
                  y: 0.865847,
                  colorBy: '',
                },
              ],
              format: {
                specifier: ',.1%',
                suffix: '',
                divideDataBy: 1,
                isAuto: false,
                decimalPlaces: 1,
                excelFormat: '#,###0.0%""',
              },
              yAxis: 1,
              metric_additional: true,
              value: '0.0%',
              numberValue: 0,
            },
          ],
          serieColor: ['#489cff', '#8BD12E', '#fb8437', '#FFC300', '#997DED'],
          categories: [
            '2023-05-30',
            '2023-05-31',
            '2023-05-01',
            '2023-06-02',
            '2023-06-03',
            '2023-06-04',
            '2023-06-05',
          ],
          activityDays: [],
          componentStyle: {
            chartTitle: '默认图表名称',
            chartTitleShow: false,
            borderShow: true,
            rowCount: 5,
          },
          dataColumn: {
            dataColumn: {
              XAxis: {
                labelForAxis: true,
              },
              YAxis: {
                labelForAxis: true,
                lineStyle: 'solid',
                labelForNumeric: true,
                labelForNumericChoice: 'maxAndMin',
              },
            },
          },
        },
        docId: 'docljh004l0',
        hidden: false,
        title: '',
        isLocked: false,
        condition: true,
        conditionGroup: '',
        bindDataSet: '测试-123',
      },
    ],
    title: '图表插槽容器',
    id: 'jn_indicator_group_chart_slot',
  },
  componentStyle: {
    groupTitle: '数据概览',
    showTitle: true,
    rowCount: 5,
  },
  allDataColumn: {
    '1': {
      columns: [
        {
          key: '101',
          title: '同比',
          fieldId: 101,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0339,
          dataField: '101',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '102',
          title: '环比',
          fieldId: 102,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0876,
          dataField: '102',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '100',
      mainTitle: 'GMV',
      tooltipText: '',
    },
    '2': {
      columns: [
        {
          key: '201',
          title: '同比',
          fieldId: 201,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0895,
          dataField: '201',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '202',
          title: '环比',
          fieldId: 202,
          displayType: 'Single',
          dataType: 'text',
          value: -0.0902,
          dataField: '202',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '200',
      mainTitle: '净销金额',
      tooltipText: '',
    },
    '3': {
      columns: [
        {
          key: '301',
          title: '同比',
          fieldId: 301,
          displayType: 'Single',
          dataType: 'text',
          value: -0.2422,
          dataField: '301',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '302',
          title: '环比',
          fieldId: 302,
          displayType: 'Single',
          dataType: 'text',
          value: -0.009,
          dataField: '302',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '300',
      mainTitle: '退款金额',
      tooltipText: '',
    },
    '4': {
      columns: [
        {
          key: '401',
          title: '同比',
          fieldId: 401,
          displayType: 'Single',
          dataType: 'text',
          value: -0.267,
          dataField: '401',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '402',
          title: '环比',
          fieldId: 402,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0204,
          dataField: '402',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '400',
      mainTitle: '退款金额占比',
      tooltipText: '',
    },
    '5': {
      columns: [
        {
          key: '501',
          title: '同比',
          fieldId: 501,
          displayType: 'Single',
          dataType: 'text',
          value: -0.2075,
          dataField: '501',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '502',
          title: '环比',
          fieldId: 502,
          displayType: 'Single',
          dataType: 'text',
          value: -0.0833,
          dataField: '502',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '500',
      mainTitle: '件单价',
      tooltipText: '',
    },
    '6': {
      columns: [
        {
          key: '601',
          title: '同比',
          fieldId: 601,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1512,
          dataField: '601',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '602',
          title: '环比',
          fieldId: 602,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1047,
          dataField: '602',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '600',
      mainTitle: '客单价',
      tooltipText: '',
    },
    '7': {
      columns: [
        {
          key: '702',
          title: '环比',
          fieldId: 702,
          displayType: 'Single',
          dataType: 'text',
          value: 0.035,
          dataField: '702',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '700',
      mainTitle: '投放金额',
      tooltipText: '',
    },
    '8': {
      columns: [
        {
          key: '802',
          title: '环比',
          fieldId: 802,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1641,
          dataField: '802',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '800',
      mainTitle: '推广ROI',
      tooltipText: '',
    },
    '9': {
      columns: [
        {
          key: '902',
          title: '环比',
          fieldId: 902,
          displayType: 'Single',
          dataType: 'text',
          value: 0.1344,
          dataField: '902',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '900',
      mainTitle: '费率',
      tooltipText: '',
    },
    '10': {
      columns: [
        {
          key: '1001',
          title: '同比',
          fieldId: 1001,
          displayType: 'Single',
          dataType: 'text',
          value: 0.4335,
          dataField: '1001',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '1002',
          title: '环比',
          fieldId: 1002,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0427,
          dataField: '1002',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '1000',
      mainTitle: '访客数',
      tooltipText: '',
    },
    '11': {
      columns: [
        {
          key: '1101',
          title: '同比',
          fieldId: 1101,
          displayType: 'Single',
          dataType: 'text',
          value: 0.218,
          dataField: '1101',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '1102',
          title: '环比',
          fieldId: 1102,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0191,
          dataField: '1102',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '1100',
      mainTitle: '成交客户数',
      tooltipText: '',
    },
    '12': {
      columns: [
        {
          key: '1201',
          title: '同比',
          fieldId: 1201,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1503,
          dataField: '1201',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '1202',
          title: '环比',
          fieldId: 1202,
          displayType: 'Single',
          dataType: 'text',
          value: -0.0227,
          dataField: '1202',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      mainField: '1200',
      mainTitle: '支付转化率',
      tooltipText: '',
    },
  },
  advanced: {
    nullFilled: '-',
    canExport: false,
  },
  cardGroupSource: {
    '1': {
      key: '100',
      title: 'GMV',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '101',
          title: '同比',
          fieldId: 101,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0339,
          dataField: '101',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '102',
          title: '环比',
          fieldId: 102,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0876,
          dataField: '102',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '4,098.83万',
      mainField: '100',
      dataField: '100',
      mainTitle: 'GMV',
      nickName: 'GMV',
      formatType: '#,###0.00"万"',
      showControl: true,
      tooltipText: '',
    },
    '2': {
      key: '200',
      title: '净销金额',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '201',
          title: '同比',
          fieldId: 201,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0895,
          dataField: '201',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '202',
          title: '环比',
          fieldId: 202,
          displayType: 'Single',
          dataType: 'text',
          value: -0.0902,
          dataField: '202',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '3,594.76万',
      mainField: '200',
      dataField: '200',
      mainTitle: '净销金额',
      nickName: '净销金额',
      formatType: '#,###0.00"万"',
      showControl: true,
      tooltipText: '',
    },
    '3': {
      key: '300',
      title: '退款金额',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '301',
          title: '同比',
          fieldId: 301,
          displayType: 'Single',
          dataType: 'text',
          value: -0.2422,
          dataField: '301',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '302',
          title: '环比',
          fieldId: 302,
          displayType: 'Single',
          dataType: 'text',
          value: -0.009,
          dataField: '302',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '504.06万',
      mainField: '300',
      dataField: '300',
      mainTitle: '退款金额',
      nickName: '退款金额',
      formatType: '#,###0.00"万"',
      showControl: true,
      tooltipText: '',
    },
    '4': {
      key: '400',
      title: '退款金额占比',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '401',
          title: '同比',
          fieldId: 401,
          displayType: 'Single',
          dataType: 'text',
          value: -0.267,
          dataField: '401',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '402',
          title: '环比',
          fieldId: 402,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0204,
          dataField: '402',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '12.30%',
      mainField: '400',
      dataField: '400',
      mainTitle: '退款金额占比',
      nickName: '退款金额占比',
      formatType: '#,###0.00%',
      showControl: true,
      tooltipText: '',
    },
    '5': {
      key: '500',
      title: '件单价',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '501',
          title: '同比',
          fieldId: 501,
          displayType: 'Single',
          dataType: 'text',
          value: -0.2075,
          dataField: '501',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '502',
          title: '环比',
          fieldId: 502,
          displayType: 'Single',
          dataType: 'text',
          value: -0.0833,
          dataField: '502',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '57.89',
      mainField: '500',
      dataField: '500',
      mainTitle: '件单价',
      nickName: '件单价',
      formatType: '#,###0.00',
      showControl: true,
      tooltipText: '',
    },
    '6': {
      key: '600',
      title: '客单价',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '601',
          title: '同比',
          fieldId: 601,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1512,
          dataField: '601',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '602',
          title: '环比',
          fieldId: 602,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1047,
          dataField: '602',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '78.93',
      mainField: '600',
      dataField: '600',
      mainTitle: '客单价',
      nickName: '客单价',
      formatType: '#,###0.00',
      showControl: true,
      tooltipText: '',
    },
    '7': {
      key: '700',
      title: '投放金额',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '702',
          title: '环比',
          fieldId: 702,
          displayType: 'Single',
          dataType: 'text',
          value: 0.035,
          dataField: '702',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '838.61万',
      mainField: '700',
      dataField: '700',
      mainTitle: '投放金额',
      nickName: '投放金额',
      formatType: '#,###0.00"万"',
      showControl: true,
      tooltipText: '',
    },
    '8': {
      key: '800',
      title: '推广ROI',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '802',
          title: '环比',
          fieldId: 802,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1641,
          dataField: '802',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '3.50',
      mainField: '800',
      dataField: '800',
      mainTitle: '推广ROI',
      nickName: '推广ROI',
      formatType: '#,###0.00',
      showControl: true,
      tooltipText: '',
    },
    '9': {
      key: '900',
      title: '费率',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '902',
          title: '环比',
          fieldId: 902,
          displayType: 'Single',
          dataType: 'text',
          value: 0.1344,
          dataField: '902',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '20.46%',
      mainField: '900',
      dataField: '900',
      mainTitle: '费率',
      nickName: '费率',
      formatType: '#,###0.00%',
      showControl: true,
      tooltipText: '',
    },
    '10': {
      key: '1000',
      title: '访客数',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '1001',
          title: '同比',
          fieldId: 1001,
          displayType: 'Single',
          dataType: 'text',
          value: 0.4335,
          dataField: '1001',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '1002',
          title: '环比',
          fieldId: 1002,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0427,
          dataField: '1002',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '721.33万',
      mainField: '1000',
      dataField: '1000',
      mainTitle: '访客数',
      nickName: '访客数',
      formatType: '#,###0.00"万"',
      showControl: true,
      tooltipText: '',
    },
    '11': {
      key: '1100',
      title: '成交客户数',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '1101',
          title: '同比',
          fieldId: 1101,
          displayType: 'Single',
          dataType: 'text',
          value: 0.218,
          dataField: '1101',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '1102',
          title: '环比',
          fieldId: 1102,
          displayType: 'Single',
          dataType: 'text',
          value: 0.0191,
          dataField: '1102',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '519,330',
      mainField: '1100',
      dataField: '1100',
      mainTitle: '成交客户数',
      nickName: '成交客户数',
      formatType: '#,###0',
      showControl: true,
      tooltipText: '',
    },
    '12': {
      key: '1200',
      title: '支付转化率',
      fieldId: '',
      displayType: 'Multi',
      children: [
        {
          key: '1201',
          title: '同比',
          fieldId: 1201,
          displayType: 'Single',
          dataType: 'text',
          value: -0.1503,
          dataField: '1201',
          nickName: '同比',
          formatType: '#,###0.00%',
          showControl: true,
        },
        {
          key: '1202',
          title: '环比',
          fieldId: 1202,
          displayType: 'Single',
          dataType: 'text',
          value: -0.0227,
          dataField: '1202',
          nickName: '环比',
          formatType: '#,###0.00%',
          showControl: true,
        },
      ],
      dataType: 'text',
      value: '7.20%',
      mainField: '1200',
      dataField: '1200',
      mainTitle: '支付转化率',
      nickName: '支付转化率',
      formatType: '#,###0.00%',
      showControl: true,
      tooltipText: '',
    },
  },
  style: {
    backgroundColor: '#ffffff',
    marginTop: '12px',
    padding: '16px',
  },
};
