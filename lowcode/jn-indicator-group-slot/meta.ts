import { DEFAULT_SOURCE } from './constants/defaultSource';
import props from './props';

const JNIndicatorGroupSlotSnippets = [
  {
    title: '指标组插槽',
    screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b034.png',
    schema: {
      componentName: 'JNIndicatorGroupSlot',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNIndicatorGroupSlotMeta = [
  {
    componentName: 'JNIndicatorGroupSlot',
    title: '指标组插槽',
    category: '布局容器类',
    screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b034.png',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNIndicatorGroupSlot',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: true,
        nestingRule: {},
      },
      props,
    },
    advanced: {
      show: false,
    },
    snippets: JNIndicatorGroupSlotSnippets,
  },
];

export default JNIndicatorGroupSlotMeta;
