import { DEFAULT_SOURCE } from './constants/defaultSource';
import props from './props';

const J<PERSON>ineChartCardSnippets = [
  {
    title: '趋势指标卡',
    screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b030.png',
    schema: {
      componentName: 'JnLineChartCard',
      props: DEFAULT_SOURCE,
    },
  },
];

const J<PERSON>ineChartCardMeta = {
  componentName: 'JnLineChartCard',
  title: '趋势指标卡',
  category: '卡片',
  group: 'MelGeek组件',
  docUrl: '',
  screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b030.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JnLineChartCard',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNLineChartCardSnippets,
};

export default { ...JNLineChartCardMeta, JNLineChartCardSnippets };
