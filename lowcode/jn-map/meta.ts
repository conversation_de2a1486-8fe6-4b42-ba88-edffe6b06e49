import props from './props';

export const JNMap = {
  componentName: 'JNMap',
  title: '全国地理图',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b035.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '图表类',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNMap',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    // supports: {
    //   events: ['onSave', 'onRemove'],
    // },
  },
  snippets: [
    {
      title: '全国地理图',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b035.png',
      schema: {
        componentName: 'JNMap',
        props: {
          type: 'innerLabel',
          inRange: 'B1',
          componentStyle: {
            zoom: 1.5,
            top: 20,
          },
          inner: {
            value: ['name'],
          },
          style: {
            height: '600px',
          },
        },
      },
    },
  ],
};

export default JNMap;
