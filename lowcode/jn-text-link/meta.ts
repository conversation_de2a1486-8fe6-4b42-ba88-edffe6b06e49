import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNTextLinkSnippets } from './snippets';
import { JNTextLinkProps } from './props';

const JNTextLinkMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNTextLink',
  group: 'MelGeek组件',
  category: '通用',
  title: '文本链接',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b037.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNTextLink',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNTextLinkProps,
    component: {},
  },
  snippets: JNTextLinkSnippets,
};

export default JNTextLinkMeta;
