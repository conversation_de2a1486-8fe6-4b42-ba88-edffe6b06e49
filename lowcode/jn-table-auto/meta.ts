import { IComponentDescription } from '../types/index';
import { props } from './props';

export const JNTableOfAutoMeta: IComponentDescription = {
  componentName: 'ComparedTable',
  title: '表格(自动分页)',
  docUrl: '',
  icon: 'https://cdn-h5.bananain.cn/icons/b013.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'ComparedTable',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '表格(自动分页)',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b013.png',
      schema: {
        componentName: 'ComparedTable',
        props: {
          dataSource: [],
          data: [
            { year: '1991', value: 72345678 },
            { year: '1992', value: 4321132 },
            { year: '1993', value: 33121112.5 },
            { year: '1994', value: 45227221 },
            { year: '1995', value: 4321221.9 },
            { year: '1996', value: 6322121 },
            { year: '1997', value: 78312213 },
            { year: '1998', value: 4192312 },
            { year: '1999', value: 6212332 },
            { year: '2000', value: 3192312 },
          ],
          border: 1,
          id: null,
          settingButtons: true,
          columns: [],
        },
      },
    },
  ],
};

export default JNTableOfAutoMeta;
