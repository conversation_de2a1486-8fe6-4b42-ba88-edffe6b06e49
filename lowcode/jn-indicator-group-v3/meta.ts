import { DEFAULT_SOURCE } from './constants/defaultSource';
import props from './props';

const JNIndicatorGroupV3Snippets = [
  {
    title: '指标组V3',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b012.png',
    schema: {
      componentName: 'JNIndicatorGroupV3',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNIndicatorGroupV3Meta = [
  {
    componentName: 'JNIndicatorGroupV3',
    title: '指标组V3',
    category: '布局容器类',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b012.png',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNIndicatorGroupV3',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: true,
        nestingRule: {},
      },
      props,
    },
    advanced: {
      show: false,
    },
    snippets: JNIndicatorGroupV3Snippets,
  },
];

export default JNIndicatorGroupV3Meta;
