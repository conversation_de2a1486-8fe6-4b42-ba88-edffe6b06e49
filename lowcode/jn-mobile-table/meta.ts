// import props from "./props";

export const JNTableMobileMeta = {
  componentName: 'JNTableMobile',
  title: '列表(移动端)',
  docUrl: '',
  icon: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b031.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNTableMobile',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    // props,
    component: {
      isContainer: false,
    },
  },
  snippets: [
    {
      title: '列表(移动端)',
      screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b031.png',
      schema: {
        componentName: 'JNTableMobile',
        props: {},
      },
    },
  ],
};

export default JNTableMobileMeta;
