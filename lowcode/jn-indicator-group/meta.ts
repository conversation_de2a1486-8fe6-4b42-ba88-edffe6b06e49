import { DEFAULT_SOURCE } from './constants/defaultSource';
import props from './props';

const JNIndicatorGroupSnippets = [
  {
    title: '指标组',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b012.png',
    schema: {
      componentName: 'JNIndicatorGroup',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNIndicatorGroupMeta = [
  {
    componentName: 'JNIndicatorGroup',
    title: '指标组',
    category: '布局容器类',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b012.png',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNIndicatorGroup',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: true,
        nestingRule: {},
      },
      props,
    },
    advanced: {
      show: false,
    },
    snippets: JNIndicatorGroupSnippets,
  },
];

export default JNIndicatorGroupMeta;
