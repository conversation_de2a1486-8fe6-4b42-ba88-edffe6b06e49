/*
 * @Author: kiki
 * @Date: 2024-05-11 11:27:36
 * @LastEditTime: 2024-05-16 17:49:09
 * @LastEditors: kiki
 * @Description:
 */
import props from './props';

const JNPageAnchorMeta = [
  {
    componentName: 'JNPageAnchor',
    title: '电梯导航',
    category: '布局容器类',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    screenshot: 'https://cdn-h5.bananain.cn/icons/icon-page-anchor.png',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNPageAnchor',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: false,
        nestingRule: {
          // 该组件允许被拖入的父组件
          parentWhitelist: ['Page'],
        },
      },
      props,
    },
    snippets: [
      {
        title: '电梯导航',
        screenshot: 'https://cdn-h5.bananain.cn/icons/icon-page-anchor.png',
        schema: {
          componentName: 'JNPageAnchor',
          props: {
            anchorItems: [],
            offsetTop: 80,
          },
        },
      },
    ],
  },
];

export default JNPageAnchorMeta;
