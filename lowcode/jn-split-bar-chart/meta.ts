import props from './props';
import { JNSplitBarChartSnippets } from './snippets';

const JNSplitBarChartMeta = {
  componentName: 'JNSplitBarChart',
  title: '分割柱状图',
  category: '图表类',
  group: 'MelGeek组件',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b022.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNSplitBarChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNSplitBarChartSnippets,
};

export default { ...JNSplitBarChartMeta, JNSplitBarChartSnippets };
