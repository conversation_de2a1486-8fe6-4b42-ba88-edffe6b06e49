import props from './props';

const snippets = [
  {
    title: '选项卡',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b029.png',
    schema: {
      componentName: 'JNTabContainer',
      props: {
        shape: 'pure',
        size: 'medium',
        excessMode: 'slide',
        items: [
          {
            title: '品类分析',
            primaryKey: 'tab_category_analysis',
          },
          {
            title: '单品结构',
            primaryKey: 'tab_item_structure',
          },
          {
            title: '商品榜单',
            primaryKey: 'tab_item_rafhnking',
          },
        ],
      },
      children: [
        {
          componentName: 'JNTabContainer.Item',
          props: {
            title: '品类分析',
            primaryKey: 'tab_category_analysis',
          },
          children: [
            // {
            //   componentName: 'ComparedTable',
            //   id: 'node_ocli465x907',
            //   props: {
            //     dataSource: [],
            //     data: [],
            //     type: 0,
            //     paginationProps: {
            //       pageSize: 20,
            //       current: 1,
            //     },
            //     ids: ['db6c8796a118f456c8cb9579', 'oe41aefad1cfa4ceb81e3c7b'],
            //     settingButtons: true,
            //     columns: [],
            //   },
            //   hidden: false,
            //   title: '',
            //   isLocked: false,
            //   condition: true,
            //   conditionGroup: '',
            // },
          ],
        },
        {
          componentName: 'JNTabContainer.Item',
          props: {
            title: '单品结构',
            primaryKey: 'tab_item_structure',
          },
          children: [],
        },
        {
          componentName: 'JNTabContainer.Item',
          props: {
            title: '商品榜单',
            primaryKey: 'tab_item_rafhnking',
          },
          children: [],
        },
      ],
    },
  },
];

const tabItemMeta = {
  componentName: 'JNTabContainer.Item',
  title: '子选项卡',

  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b029.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNTabContainer',
    main: 'lib/index.js',
    destructuring: true,
    subName: 'Item',
  },
  configure: {
    props: [
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'title',
            'zh-CN': '选项卡标题',
          },
          tip: 'title | 选项卡标题',
        },
        name: 'title',
        description: '选项卡标题',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'closeable',
            'zh-CN': '单个选项卡是否可关闭',
          },
          tip: 'closeable | 单个选项卡是否可关闭',
        },
        name: 'closeable',
        description: '单个选项卡是否可关闭',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'disabled',
            'zh-CN': '选项卡是否被禁用',
          },
          tip: 'disabled | 选项卡是否被禁用',
        },
        name: 'disabled',
        description: '选项卡是否被禁用',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'prefix',
            'zh-CN': '样式类名的品牌前缀',
          },
          tip: 'prefix | 样式类名的品牌前缀',
        },
        name: 'prefix',
        description: '样式类名的品牌前缀',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'locale',
            'zh-CN': '国际化文案对象，属性',
          },
          tip: 'locale | 国际化文案对象，属性为组件的 displayName',
        },
        name: 'locale',
        description: '国际化文案对象，属性为组件的 displayName',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'pure',
            'zh-CN': '是否开启 Pure ',
          },
          tip: 'pure | 是否开启 Pure Render 模式，会提高性能，但是也会带来副作用',
        },
        name: 'pure',
        description: '是否开启 Pure Render 模式，会提高性能，但是也会带来副作用',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'warning',
            'zh-CN': '是否在开发模式下显示',
          },
          tip: 'warning | 是否在开发模式下显示组件属性被废弃的 warning 提示',
        },
        name: 'warning',
        description: '是否在开发模式下显示组件属性被废弃的 warning 提示',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'rtl',
            'zh-CN': '是否开启 rtl 模',
          },
          tip: 'rtl | 是否开启 rtl 模式',
        },
        name: 'rtl',
        description: '是否开启 rtl 模式',
        setter: {
          componentName: 'StringSetter',
          isRequired: false,
          initialValue: '',
        },
      },
    ],
    supports: {
      events: [
        {
          name: 'onClick',
        },
        {
          name: 'onChange',
        },
      ],
      style: true,
    },
    component: {
      isContainer: true,
      disableBehaviors: '*',
    },
    advanced: {
      hideSelectTools: true,
      callbacks: {
        onMouseDownHook: () => false,
        onClickHook: () => false,
      },
    },
  },
  snippets,
};

const TabContainerMeta = [
  {
    componentName: 'JNTabContainer',
    title: '选项卡',
    group: 'MelGeek组件',
    category: '布局容器类', //
    tags: ['MelGeek组件'],
    docUrl: '',
    screenshot:
      'https://img.alicdn.com/imgextra/i4/O1CN01mh9LPG268B90t8DaA_!!6000000007616-55-tps-56-56.svg',
    devMode: 'proCode',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNTabContainer',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: true,
        nestingRule: {
          childWhitelist: ['JNTabContainer.Item'],
        },
      },
      props,
      supports: {
        style: true,
      },
    },
    snippets,
  },
  tabItemMeta,
];

export default TabContainerMeta;
