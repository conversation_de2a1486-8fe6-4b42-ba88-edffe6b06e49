import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNQuickFilterProps } from './props';

export const JNSubFilterBarMeta: IComponentDescription = {
  componentName: 'JNSubFilterBar',
  title: '筛选器',
  docUrl: '',
  icon: 'https://cdn-h5.bananain.cn/icons/b025.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.9',
    exportName: 'JNSubFilterBar',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props: JNQuickFilterProps,
    component: {
      isContainer: false,
      nestingRule: {},
      disableBehaviors: ['copy', 'move'],
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },

  snippets: [
    {
      title: '筛选器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b025.png',
      schema: {
        componentName: 'JNSubFilterBar',
        props: {
          columns: [],
          componentStyle: {
            styleType: '通用',
          },
        },
      },
    },
  ],
};

export default JNSubFilterBarMeta;
