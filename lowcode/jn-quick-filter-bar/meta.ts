import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNQuickFilterProps } from './props';

export const JNQuickFilterMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNQuickFilter',
  group: 'MelGeek组件',
  category: '表格类',
  title: '快捷筛选器',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b025.png',
  devMode: 'proCode',
  tags: ['MelGeek组件'],

  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNQuickFilter',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props: JNQuickFilterProps,
    component: {
      isContainer: false,
      nestingRule: {},
      disableBehaviors: ['copy', 'move'],
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },

  snippets: [
    {
      title: '快捷筛选器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b025.png',
      schema: {
        componentName: 'JNQuickFilter',
        props: {
          columns: [],
          componentStyle: {
            styleType: '通用',
          },
        },
      },
    },
  ],
};

export default JNQuickFilterMeta;
