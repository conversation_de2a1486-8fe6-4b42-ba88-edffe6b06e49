import { leftCardProps } from './defaultValue'; // 默认数据
import props from './props';

export const JNObjectCardMeta = {
  componentName: 'ObjectCardOfSingleGraph',
  title: '单图达成率+指标/双指标',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b009.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'ObjectCardOfSingleGraph',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '单图达成率+指标/双指标',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b009.png',
      schema: {
        componentName: 'ObjectCardOfSingleGraph',
        props: leftCardProps,
      },
    },
  ],
};

export default JNObjectCardMeta;
