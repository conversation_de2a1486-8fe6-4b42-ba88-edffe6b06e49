import props from './props';
import { JNMixChartSnippets } from './snippets';

const JNMixChartMeta = {
  componentName: 'JNMixChart',
  title: '通用混合图',
  category: '图表类',
  group: 'MelGeek组件',
  docUrl: '',
  devMode: 'proCode',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b024.png',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNMixChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNMixChartSnippets,
};

export default { ...JNMixChartMeta, JNMixChartSnippets };
