import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNScatterChartSnippets } from './snippets';
import { JNScatter<PERSON>hartProps } from './props';

const JNScatterChartMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNScatterChart',
  group: 'MelGeek组件',
  category: '图表类',
  title: '散点气泡图',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b040.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNScatterChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNScatterChartProps,
    component: {},
  },
  snippets: JNScatterChartSnippets,
};

export default JNScatterChartMeta;
