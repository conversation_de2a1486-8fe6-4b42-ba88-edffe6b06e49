import props from './props';

export const JNNavMobileMeta = {
  componentName: 'JNNavMobile',
  title: '导航栏(移动端)',
  docUrl: '',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '布局容器类',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNNavMobile',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
    },
  },
  snippets: [
    {
      title: '导航栏(移动端)',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b026.png',
      schema: {
        componentName: 'JNNavMobile',
        props: {
          navItems: [
            {
              title: '电商',
              icon: 'icon-a-tubiaowangge-16110',
            },
            {
              title: '商品',
              icon: 'icon-a-tubiaowangge-16113',
            },
            {
              title: '零售',
              icon: 'icon-a-tubiaowangge-1655',
            },
          ],
        },
      },
    },
  ],
};

export default JNNavMobileMeta;
