import props from './props';

const JNIndicatorGroupV2Meta = [
  {
    componentName: 'JNIndicatorGroupV2',
    title: '指标组v2.0',
    category: '布局容器类',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b028.png',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNIndicatorGroupV2',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: false,
        nestingRule: {},
      },
      props,
    },
    snippets: [
      {
        title: '指标组v2.0',
        screenshot: 'https://cdn-h5.bananain.cn/icons/b028.png',
        schema: {
          componentName: 'JNIndicatorGroupV2',
          props: {
            style: {
              marginTop: '12px',
              padding: '14px',
            },
          },
        },
      },
    ],
  },
];

export default JNIndicatorGroupV2Meta;
