import { IComponentDescription } from '../types/index';
import { props } from './props';

export const MixColumnTableMeta: IComponentDescription = {
  componentName: 'MixColumnTable',
  title: '合并表格',
  isHidden: true,
  docUrl: '',
  icon: 'https://cdn-h5.bananain.cn/icons/b013.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'MixColumnTable',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '合并表格',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b013.png',
      schema: {
        componentName: 'MixColumnTable',
        props: {
          dataSource: [],
          border: 1,
          id: null,
          settingButtons: true,
          columns: [],
        },
      },
    },
  ],
};

export default MixColumnTableMeta;
