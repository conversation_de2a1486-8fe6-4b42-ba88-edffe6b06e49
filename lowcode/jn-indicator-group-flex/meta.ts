import { DEFAULT_SOURCE } from './constants/defaultSource';
import props from './props';

const JNIndicatorGroupSnippets = [
  {
    title: '指标列表',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b023.png',
    schema: {
      componentName: 'JNIndicatorGroupFlex',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNIndicatorGroupMeta = [
  {
    componentName: 'JNIndicatorGroupFlex',
    title: '指标列表',
    category: '布局容器类',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b023.png',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNIndicatorGroupFlex',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        // isContainer: true,
        nestingRule: {},
      },
      props,
    },
    advanced: {
      show: false,
    },
    snippets: JNIndicatorGroupSnippets,
  },
];

export default JNIndicatorGroupMeta;
