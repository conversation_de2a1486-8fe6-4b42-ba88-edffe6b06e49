import props from './props';

export const JNPercentCumulateAreaChartMeta = {
  componentName: 'JNPercentCumulateAreaChart',
  title: '百分比堆积面积图',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b036.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '图表类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNPercentCumulateAreaChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '百分比堆积面积图',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b036.png',
      schema: {
        componentName: 'JNPercentCumulateAreaChart',
        props: {
          style: {
            margin: '12px 0 0 0',
            padding: '16px',
            background: '#fff',
          },
        },
      },
    },
  ],
};

export default JNPercentCumulateAreaChartMeta;
