import { leftCardProps } from './defaultValue'; // 默认数据
import props from './props';

export const JNObjectCardMeta = {
  componentName: 'ObjectCardOfSingleIndex',
  title: '单指标/双指标',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b004.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'ObjectCardOfSingleIndex',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '单指标/双指标',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b004.png',
      schema: {
        componentName: 'ObjectCardOfSingleIndex',
        props: leftCardProps,
      },
    },
  ],
};

export default JNObjectCardMeta;
