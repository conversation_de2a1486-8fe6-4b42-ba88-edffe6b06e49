import props from './props';
import { DEFAULT_SOURCE } from './constants/defaultSource';

const JNLineChartSnippets = [
  {
    title: '通用折线图',
    screenshot: 'https://cdn-h5.bananain.cn/icons/001.png',
    schema: {
      componentName: 'JNLineChart',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNLineChartMeta = {
  componentName: 'JNLineChart',
  title: '通用折线图',
  category: '图表类',
  group: 'MelGeek组件',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/001.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNLineChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNLineChartSnippets,
};

export default { ...JNLineChartMeta, JNLineChartSnippets };
