import defaultValue from './defaultValue'; // 默认数据
import props from './props';

export const JNObjectCardMeta = {
  componentName: 'JNProgressCard',
  title: '指标卡-进度条',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b006.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNProgressCard',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
  },
  snippets: [
    {
      title: '指标卡-进度条',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b006.png',
      schema: {
        componentName: 'JNProgressCard',
        props: defaultValue,
      },
    },
  ],
};

export default JNObjectCardMeta;
