// =========================蕉内 对象指标卡组件

import { leftCardProps } from './defaultValue';
import props from './props';

export const JNObjectCardMeta = {
  componentName: 'JNObjectCard',
  title: '对象指标卡',
  screenshot: 'https://cdn-h5.bananain.cn/icons/002.png',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNObjectCard',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '对象指标卡',
      screenshot: 'https://cdn-h5.bananain.cn/icons/002.png',
      schema: {
        componentName: 'JNObjectCard',
        props: leftCardProps,
      },
    },
  ],
};

export default JNObjectCardMeta;
